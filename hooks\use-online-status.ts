"use client"

import { useState, useEffect } from "react"

async function checkConnectivityWithTimeout(timeoutMs = 2500): Promise<boolean> {
  if (typeof navigator !== "undefined" && !navigator.onLine) return false

  const controller = new AbortController()
  const timeout = setTimeout(() => controller.abort(), timeoutMs)

  try {
    // Use a cross-origin no-cors request so SW and caches don't mask offline
    // Any opaque success counts as online; network failure throws
    await fetch("https://www.google.com/generate_204", {
      method: "GET",
      mode: "no-cors",
      cache: "no-store",
      signal: controller.signal,
    })
    return true
  } catch {
    return false
  } finally {
    clearTimeout(timeout)
  }
}

export function useOnlineStatus() {
  const [isOnline, setIsOnline] = useState(true) // Always start with true to avoid hydration mismatch
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    if (typeof window === "undefined") return

    const verify = async () => {
      const ok = await checkConnectivityWithTimeout()
      setIsOnline(ok)
      setIsInitialized(true)
    }

    const handleOnline = () => verify()
    const handleOffline = () => setIsOnline(false)

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    // Verify on mount in case navigator.onLine is stale
    verify()

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  // Return true until we've verified the actual status to prevent hydration mismatch
  return isInitialized ? isOnline : true
}
