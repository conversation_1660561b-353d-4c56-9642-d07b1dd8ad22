"use client"

import { sqliteClient } from './sqlite'
import { sqliteDataClient } from './sqlite-data-client'

export interface MigrationResult {
  table: string
  migrated: number
  errors: string[]
}

export interface MigrationSummary {
  totalRecords: number
  totalMigrated: number
  totalErrors: number
  results: MigrationResult[]
  duration: number
}

class MigrationTool {
  async migrateAllData(): Promise<MigrationSummary> {
    const startTime = Date.now()
    
    console.log('Migration from IndexedDB to SQLite is no longer supported.')
    console.log('Dexie.js has been removed from the application.')
    console.log('If you need to migrate data, please restore from a backup or re-enter data manually.')
    
    const summary: MigrationSummary = {
      totalRecords: 0,
      totalMigrated: 0,
      totalErrors: 1,
      results: [{
        table: 'migration',
        migrated: 0,
        errors: ['Migration no longer supported - Dexie.js has been removed from the application']
      }],
      duration: Date.now() - startTime
    }

    return summary
  }

  // Check if migration is needed
  async isMigrationNeeded(): Promise<boolean> {
    console.log('Migration is no longer supported - Dexie.js has been removed')
    return false
  }

  // Clear SQLite data (for testing)
  async clearSQLiteData(): Promise<void> {
    const tables = [
      'radiology', 'cultures', 'doctor_notes', 'medication_adherence',
      'medications', 'medication_names', 'lab_values', 'vital_signs',
      'patients', 'unit_types'
    ]

    for (const table of tables) {
      await sqliteClient.run(`DELETE FROM ${table}`)
    }
  }
}

export const migrationTool = new MigrationTool()
