"use client"

import { useState, useEffect } from "react"
import { Activity, Users, LogOut } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { SyncIndicator } from "@/components/sync-indicator"
import { getSession, logout } from "@/lib/auth"
import { useRouter } from "next/navigation"
import Link from "next/link"

export function AppHeader() {
  const router = useRouter()
  const [session, setSession] = useState<any>(null)

  useEffect(() => {
    const currentSession = getSession()
    setSession(currentSession)
  }, [])

  const handleLogout = () => {
    logout()
    router.push("/login")
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between px-4">
        <div className="flex items-center gap-2">
          <Activity className="h-6 w-6 text-blue-600" />
          <h1 className="text-lg font-semibold">ICU Patient Management</h1>
        </div>

        <div className="flex items-center gap-4">
          {/* Sync Status */}
          <SyncIndicator />

          {/* Admin Link */}
          {session?.user.role === "admin" && (
            <Button variant="ghost" size="sm" asChild>
              <Link href="/admin">
                <Users className="h-4 w-4 mr-2" />
                Admin
              </Link>
            </Button>
          )}

          {/* Logout */}
          <Button variant="ghost" size="sm" onClick={handleLogout}>
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>
    </header>
  )
}
