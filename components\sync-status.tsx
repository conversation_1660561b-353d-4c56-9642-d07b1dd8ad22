"use client"

import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { useSyncStatus } from '@/hooks/use-sync-status'
import Link from 'next/link'
import {
  Wifi,
  WifiOff,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  Database,
  Loader2,
  Settings
} from 'lucide-react'

interface SyncStatusData {
  isOnline: boolean
  isSyncing: boolean
  pendingChanges: number
  lastSync: string | null
  tableStatus: Array<{
    table: string
    pendingChanges: number
    lastSync: string | null
  }>
}

interface SyncStatusProps {
  variant?: 'compact' | 'detailed'
  showManualSync?: boolean
  refreshInterval?: number
}

export function SyncStatus({
  variant = 'compact',
  showManualSync = true,
  refreshInterval = 5000
}: SyncStatusProps) {
  const { syncStatus, isLoading, manualSync } = useSyncStatus({ refreshInterval })
  const [isManualSyncing, setIsManualSyncing] = useState(false)
  const { toast } = useToast()

  const handleManualSync = async () => {
    if (!syncStatus?.isOnline) {
      toast({
        title: "Sync Failed",
        description: "Cannot sync while offline. Please check your internet connection.",
        variant: "destructive"
      })
      return
    }

    setIsManualSyncing(true)
    try {
      const results = await manualSync()

      const totalPushed = results.reduce((sum, r) => sum + r.pushed, 0)
      const totalPulled = results.reduce((sum, r) => sum + r.pulled, 0)
      const totalConflicts = results.reduce((sum, r) => sum + r.conflicts, 0)
      const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0)

      if (totalErrors > 0) {
        toast({
          title: "Sync Completed with Errors",
          description: `Pushed: ${totalPushed}, Pulled: ${totalPulled}, Conflicts: ${totalConflicts}, Errors: ${totalErrors}`,
          variant: "destructive"
        })
      } else {
        toast({
          title: "Sync Completed",
          description: `Pushed: ${totalPushed}, Pulled: ${totalPulled}, Conflicts: ${totalConflicts}`,
          variant: "default"
        })
      }
    } catch (error) {
      toast({
        title: "Sync Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      })
    } finally {
      setIsManualSyncing(false)
    }
  }

  const getStatusIcon = () => {
    if (isLoading) return <Loader2 className="h-4 w-4 animate-spin" />
    if (!syncStatus?.isOnline) return <WifiOff className="h-4 w-4 text-red-500" />
    if (syncStatus.isSyncing) return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
    if (syncStatus.pendingChanges > 0) return <AlertCircle className="h-4 w-4 text-yellow-500" />
    return <CheckCircle className="h-4 w-4 text-green-500" />
  }

  const getStatusText = () => {
    if (isLoading) return "Loading..."
    if (!syncStatus?.isOnline) return "Offline"
    if (syncStatus.isSyncing) return "Syncing..."
    if (syncStatus.pendingChanges > 0) return `${syncStatus.pendingChanges} pending`
    return "Up to date"
  }

  const getStatusVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    if (isLoading) return "secondary"
    if (!syncStatus?.isOnline) return "destructive"
    if (syncStatus.isSyncing) return "default"
    if (syncStatus.pendingChanges > 0) return "outline"
    return "default"
  }

  const formatLastSync = (lastSync: string | null) => {
    if (!lastSync) return "Never"
    const date = new Date(lastSync)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    
    if (diffMins < 1) return "Just now"
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return date.toLocaleDateString()
  }

  if (variant === 'compact') {
    return (
      <div className="flex items-center gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 px-2">
              {getStatusIcon()}
              <span className="ml-1 text-sm">{getStatusText()}</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Sync Status</h4>
                <Badge variant={getStatusVariant()}>
                  {syncStatus?.isOnline ? <Wifi className="h-3 w-3 mr-1" /> : <WifiOff className="h-3 w-3 mr-1" />}
                  {syncStatus?.isOnline ? "Online" : "Offline"}
                </Badge>
              </div>
              
              {syncStatus && (
                <>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-muted-foreground">Pending Changes</div>
                      <div className="font-medium">{syncStatus.pendingChanges}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Last Sync</div>
                      <div className="font-medium">{formatLastSync(syncStatus.lastSync)}</div>
                    </div>
                  </div>

                  {syncStatus.pendingChanges > 0 && (
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Pending by table:</div>
                      <div className="space-y-1">
                        {syncStatus.tableStatus
                          .filter(table => table.pendingChanges > 0)
                          .map(table => (
                            <div key={table.table} className="flex justify-between text-xs">
                              <span className="capitalize">{table.table.replace('_', ' ')}</span>
                              <span>{table.pendingChanges}</span>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}

                  {showManualSync && (
                    <>
                      <Separator />
                      <div className="space-y-2">
                        <Button
                          onClick={handleManualSync}
                          disabled={!syncStatus.isOnline || syncStatus.isSyncing || isManualSyncing}
                          size="sm"
                          className="w-full"
                        >
                          {isManualSyncing ? (
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          ) : (
                            <RefreshCw className="h-4 w-4 mr-2" />
                          )}
                          {isManualSyncing ? "Syncing..." : "Sync Now"}
                        </Button>
                        <Link href="/sync-status">
                          <Button variant="outline" size="sm" className="w-full">
                            <Settings className="h-4 w-4 mr-2" />
                            Sync Settings
                          </Button>
                        </Link>
                      </div>
                    </>
                  )}
                </>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    )
  }

  // Detailed variant
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Sync Status
        </CardTitle>
        <CardDescription>
          Real-time synchronization status and pending changes
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading sync status...</span>
          </div>
        ) : syncStatus ? (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {syncStatus.isOnline ? (
                  <Wifi className="h-4 w-4 text-green-500" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-500" />
                )}
                <span className="font-medium">
                  {syncStatus.isOnline ? "Online" : "Offline"}
                </span>
              </div>
              <Badge variant={getStatusVariant()}>
                {getStatusIcon()}
                <span className="ml-1">{getStatusText()}</span>
              </Badge>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Pending Changes</div>
                <div className="text-2xl font-bold">{syncStatus.pendingChanges}</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-muted-foreground">Last Sync</div>
                <div className="text-lg font-medium">{formatLastSync(syncStatus.lastSync)}</div>
              </div>
            </div>

            {syncStatus.pendingChanges > 0 && (
              <div className="space-y-3">
                <Separator />
                <div>
                  <h4 className="text-sm font-medium mb-2">Pending Changes by Table</h4>
                  <div className="space-y-2">
                    {syncStatus.tableStatus
                      .filter(table => table.pendingChanges > 0)
                      .map(table => (
                        <div key={table.table} className="flex items-center justify-between">
                          <span className="text-sm capitalize">
                            {table.table.replace('_', ' ')}
                          </span>
                          <Badge variant="outline">{table.pendingChanges}</Badge>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            )}

            {showManualSync && (
              <>
                <Separator />
                <Button 
                  onClick={handleManualSync}
                  disabled={!syncStatus.isOnline || syncStatus.isSyncing || isManualSyncing}
                  className="w-full"
                >
                  {isManualSyncing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2" />
                  )}
                  {isManualSyncing ? "Syncing..." : "Sync Now"}
                </Button>
              </>
            )}
          </>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            Failed to load sync status
          </div>
        )}
      </CardContent>
    </Card>
  )
}
