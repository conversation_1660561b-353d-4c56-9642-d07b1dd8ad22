"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { syncEngine } from '@/lib/sync-engine'
import { SyncStatus } from '@/components/sync-status'
import { 
  ArrowLeft,
  Wifi, 
  WifiOff, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Database,
  Loader2,
  Activity,
  Trash2
} from 'lucide-react'
import Link from 'next/link'

interface SyncResult {
  table: string
  pushed: number
  pulled: number
  conflicts: number
  errors: string[]
}

export default function SyncStatusPage() {
  const [syncResults, setSyncResults] = useState<SyncResult[]>([])
  const [isManualSyncing, setIsManualSyncing] = useState(false)
  const [lastSyncTime, setLastSyncTime] = useState<string | null>(null)
  const { toast } = useToast()

  const handleManualSync = async () => {
    setIsManualSyncing(true)
    try {
      const results = await syncEngine.manualSync()
      setSyncResults(results)
      setLastSyncTime(new Date().toISOString())
      
      const totalPushed = results.reduce((sum, r) => sum + r.pushed, 0)
      const totalPulled = results.reduce((sum, r) => sum + r.pulled, 0)
      const totalConflicts = results.reduce((sum, r) => sum + r.conflicts, 0)
      const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0)

      if (totalErrors > 0) {
        toast({
          title: "Sync Completed with Errors",
          description: `Pushed: ${totalPushed}, Pulled: ${totalPulled}, Conflicts: ${totalConflicts}, Errors: ${totalErrors}`,
          variant: "destructive"
        })
      } else {
        toast({
          title: "Sync Completed Successfully",
          description: `Pushed: ${totalPushed}, Pulled: ${totalPulled}, Conflicts: ${totalConflicts}`,
          variant: "default"
        })
      }
    } catch (error) {
      toast({
        title: "Sync Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      })
    } finally {
      setIsManualSyncing(false)
    }
  }

  const handleCleanupDeleted = async () => {
    try {
      const deletedCount = await syncEngine.cleanupDeletedRecords()
      toast({
        title: "Cleanup Completed",
        description: `Removed ${deletedCount} deleted records from local storage`,
        variant: "default"
      })
    } catch (error) {
      toast({
        title: "Cleanup Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      })
    }
  }

  const formatTableName = (tableName: string) => {
    return tableName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
        <div className="flex items-center gap-2">
          <Database className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Sync Status & Management</h1>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Sync Status */}
        <SyncStatus variant="detailed" showManualSync={true} />

        {/* Sync Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Sync Actions
            </CardTitle>
            <CardDescription>
              Manual sync operations and maintenance tasks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleManualSync}
              disabled={isManualSyncing}
              className="w-full"
              size="lg"
            >
              {isManualSyncing ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              {isManualSyncing ? "Syncing..." : "Force Sync Now"}
            </Button>

            <Separator />

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Maintenance</h4>
              <Button 
                onClick={handleCleanupDeleted}
                variant="outline"
                className="w-full"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Cleanup Deleted Records
              </Button>
            </div>

            {lastSyncTime && (
              <>
                <Separator />
                <div className="text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Last manual sync: {new Date(lastSyncTime).toLocaleString()}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Sync Results */}
      {syncResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Last Sync Results</CardTitle>
            <CardDescription>
              Detailed results from the most recent sync operation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {syncResults.map((result) => (
                <div key={result.table} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium">{formatTableName(result.table)}</h4>
                    <div className="flex items-center gap-2">
                      {result.errors.length > 0 ? (
                        <Badge variant="destructive">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {result.errors.length} errors
                        </Badge>
                      ) : (
                        <Badge variant="default">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Success
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-muted-foreground">Pushed</div>
                      <div className="font-medium">{result.pushed}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Pulled</div>
                      <div className="font-medium">{result.pulled}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Conflicts</div>
                      <div className="font-medium">{result.conflicts}</div>
                    </div>
                  </div>

                  {result.errors.length > 0 && (
                    <div className="mt-3">
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          <div className="space-y-1">
                            <div className="font-medium">Errors:</div>
                            {result.errors.map((error, index) => (
                              <div key={index} className="text-sm">
                                • {error}
                              </div>
                            ))}
                          </div>
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Information */}
      <Card>
        <CardHeader>
          <CardTitle>About Sync</CardTitle>
          <CardDescription>
            Understanding the synchronization process
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium">Automatic Sync</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Runs every 5 seconds when online</li>
                <li>• Syncs after any data changes</li>
                <li>• Resumes when connection is restored</li>
                <li>• Uses conflict resolution (latest wins)</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium">Status Indicators</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Up to date - all changes synced</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-yellow-500" />
                  <span>Pending changes - waiting to sync</span>
                </div>
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4 text-blue-500" />
                  <span>Syncing - operation in progress</span>
                </div>
                <div className="flex items-center gap-2">
                  <WifiOff className="h-4 w-4 text-red-500" />
                  <span>Offline - no internet connection</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
