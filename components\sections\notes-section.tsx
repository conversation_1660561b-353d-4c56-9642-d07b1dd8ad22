"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Plus, Edit, Trash2, Eye } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { dataClient } from "@/lib/data-client"
import { getSession } from "@/lib/auth"
import type { DoctorNote } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"

interface NotesSectionProps {
  patientId: string
}

export function NotesSection({ patientId }: NotesSectionProps) {
  const [notes, setNotes] = useState<DoctorNote[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [editingNote, setEditingNote] = useState<DoctorNote | null>(null)
  const [viewingNote, setViewingNote] = useState<DoctorNote | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split("T")[0],
    content: "",
    doctor_name: "",
  })
  const { toast } = useToast()

  useEffect(() => {
    loadNotes()
    // Set default doctor name from session
    const session = getSession()
    if (session) {
      setFormData((prev) => ({ ...prev, doctor_name: session.user.username }))
    }
  }, [patientId])

  const loadNotes = async () => {
    try {
      const notesData = await db.doctor_notes.where("patient_id").equals(patientId).toArray()

      // Sort by date in descending order (most recent first)
      notesData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

      setNotes(notesData)
    } catch (error) {
      console.error("Error loading notes:", error)
    }
  }

  const resetForm = () => {
    const session = getSession()
    setFormData({
      date: new Date().toISOString().split("T")[0],
      content: "",
      doctor_name: session?.user.username || "",
    })
    setEditingNote(null)
  }

  const handleEdit = (note: DoctorNote) => {
    setEditingNote(note)
    setFormData({
      date: note.date,
      content: note.content,
      doctor_name: note.doctor_name || "",
    })
    setIsDialogOpen(true)
  }

  const handleView = (note: DoctorNote) => {
    setViewingNote(note)
    setIsViewDialogOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.content.trim()) {
      toast({
        title: "Error",
        description: "Please enter note content.",
        variant: "destructive",
      })
      return
    }

    try {
      const noteData = {
        patient_id: patientId,
        date: formData.date,
        content: formData.content,
        doctor_name: formData.doctor_name,
      }

      if (editingNote) {
        await dataClient.updateDoctorNote(editingNote.id, noteData)
      } else {
        await dataClient.insertDoctorNote(noteData)
      }

      await loadNotes()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: "Note saved",
        description: "Doctor note has been successfully saved.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save doctor note.",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (note: DoctorNote) => {
    try {
      await dataClient.deleteDoctorNote(note.id)
      await loadNotes()

      toast({
        title: "Note deleted",
        description: "Doctor note has been removed.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete doctor note.",
        variant: "destructive",
      })
    }
  }

  const filteredNotes = notes.filter((note) =>
    note.content.toLowerCase().includes(searchQuery.trim().toLowerCase())
  )

  const groupedByDate = (() => {
    const groups: Record<string, DoctorNote[]> = {}
    for (const note of filteredNotes) {
      const key = note.date.includes("T") ? note.date.split("T")[0] : note.date
      if (!groups[key]) groups[key] = []
      groups[key].push(note)
    }
    const orderedDates = Object.keys(groups).sort(
      (a, b) => new Date(b).getTime() - new Date(a).getTime()
    )
    return { groups, orderedDates }
  })()

  const formatDateLabel = (dateStr: string) =>
    new Date(dateStr).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Doctor Notes</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>{editingNote ? "Edit Note" : "Add Note"}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="doctor_name">Doctor Name</Label>
                  <Input
                    id="doctor_name"
                    placeholder="Doctor name"
                    value={formData.doctor_name}
                    onChange={(e) => setFormData({ ...formData, doctor_name: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Note Content</Label>
                <Textarea
                  id="content"
                  placeholder="Enter your note here..."
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  rows={6}
                  required
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">{editingNote ? "Update Note" : "Add Note"}</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent className="p-1">
        <div className="mb-4">
          <Input
            placeholder="Search notes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {notes.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No doctor notes recorded yet.</div>
        ) : filteredNotes.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No notes match your search.</div>
        ) : (
          <Accordion
            type="multiple"
            className="w-full"
            defaultValue={
              groupedByDate.orderedDates.length ? [groupedByDate.orderedDates[0]] : []
            }
          >
            {groupedByDate.orderedDates.map((dateKey) => (
              <AccordionItem value={dateKey} key={dateKey}>
                <AccordionTrigger>{formatDateLabel(dateKey)}</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4">
                    {groupedByDate.groups[dateKey].map((note) => (
                      <Card key={note.id} className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            {note.doctor_name && (
                              <p className="text-sm text-muted-foreground">Dr. {note.doctor_name}</p>
                            )}
                          </div>
                          <div className="flex gap-2">
                            <Button variant="ghost" size="sm" onClick={() => handleView(note)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => handleEdit(note)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => handleDelete(note)}>
                              <Trash2 className="h-4 w-4 text-red-600" />
                            </Button>
                          </div>
                        </div>
                        <div className="text-sm whitespace-pre-wrap">{note.content}</div>
                      </Card>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </CardContent>

      {/* View Note Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Note Details</DialogTitle>
          </DialogHeader>
          {viewingNote && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Date</Label>
                  <p className="text-sm">{new Date(viewingNote.date).toLocaleDateString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Doctor</Label>
                  <p className="text-sm">{viewingNote.doctor_name ? `Dr. ${viewingNote.doctor_name}` : "-"}</p>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Note Content</Label>
                <div className="mt-2 p-3 bg-muted rounded-md">
                  <p className="text-sm whitespace-pre-wrap">{viewingNote.content}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  )
}
