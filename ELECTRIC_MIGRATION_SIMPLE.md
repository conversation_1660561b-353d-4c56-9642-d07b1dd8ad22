# Simple Electric SQL Migration Plan

## Overview
Replace Dexie with Electric SQL for offline-first + Supabase sync. Electric handles reads, Next.js API handles writes.

## What We're Building
- **Offline**: Local database (works like Dexie)
- **Online**: Syncs with Supabase via Electric shapes
- **Writes**: Always go through Next.js API (simple)

## Step-by-Step Migration

### 1. Install Packages
```bash
npm install @electric-sql/react @electric-sql/client
npm uninstall dexie
```

### 2. Create Electric SQL Client
**File**: `lib/electric.ts`
```typescript
"use client"

import { makeElectricContext } from '@electric-sql/react'
import { Electric } from './generated/client'

export const { ElectricProvider, useElectric } = makeElectricContext<Electric>()
```

### 3. Create Shape Proxy API
**File**: `app/api/shape-proxy/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const table = searchParams.get('table')
  
  // Forward to Electric service
  const electricUrl = process.env.ELECTRIC_URL || 'http://localhost:5133'
  const response = await fetch(`${electricUrl}/v1/shape?table=${table}`)
  
  return NextResponse.json(await response.json())
}
```

### 4. Update Data Client
**File**: `lib/data-client.ts`
```typescript
"use client"

import { useShape } from '@electric-sql/react'

// For reads - use Electric shapes
export function usePatients() {
  return useShape({
    url: '/api/shape-proxy?table=patients'
  })
}

// For writes - use Next.js API
export async function createPatient(patient: any) {
  const response = await fetch('/api/patients', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(patient)
  })
  return response.json()
}
```

### 5. Update Components
**File**: `components/sections/medications-section.tsx`
```typescript
import { usePatients } from '@/lib/data-client'

export function MedicationsSection() {
  const { data: patients } = usePatients()
  
  // Use patients data directly
  // Write operations go through API calls
}
```

### 6. Environment Variables
```env
ELECTRIC_URL=http://localhost:5133
```

## How It Works

### Offline Mode
- App works with local data
- No network calls
- Same user experience

### Online Mode
- Electric syncs data from Supabase
- Components update automatically
- Writes go to Supabase via API

## Key Points
- **Electric SQL**: Only for reading/syncing data
- **Next.js API**: Handle all writes
- **No complex database setup**: Electric handles it
- **Simple pattern**: Follows official docs exactly

## Testing
1. Test offline functionality
2. Test online sync
3. Test write operations
4. Verify no crashes

## Rollback
If issues occur, revert to previous commit with Dexie.
