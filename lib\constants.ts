// Static data constants to replace lookup tables
// These replace the database tables: user_roles, culture_statuses, radiology_scan_types, radiology_statuses, gender_types

export const USER_ROLES = [
  'admin',
  'user'
] as const

export const CULTURE_STATUSES = [
  'awaiting results',
  'completed'
] as const

export const RADIOLOGY_SCAN_TYPES = [
  'X-ray',
  'CT', 
  'MRI',
  'Ultrasound',
  'Echo',
  'Doppler',
  'Other'
] as const

export const RADIOLOGY_STATUSES = [
  'scheduled',
  'completed'
] as const

export const GENDER_TYPES = [
  'Male',
  'Female'
] as const

// Type definitions for the constants
export type UserRole = typeof USER_ROLES[number]
export type CultureStatus = typeof CULTURE_STATUSES[number]
export type RadiologyScanType = typeof RADIOLOGY_SCAN_TYPES[number]
export type RadiologyStatus = typeof RADIOLOGY_STATUSES[number]
export type GenderType = typeof GENDER_TYPES[number]

// Helper functions for validation
export const isValidUserRole = (role: string): role is UserRole => {
  return USER_ROLES.includes(role as UserRole)
}

export const isValidCultureStatus = (status: string): status is CultureStatus => {
  return CULTURE_STATUSES.includes(status as CultureStatus)
}

export const isValidRadiologyScanType = (scanType: string): scanType is RadiologyScanType => {
  return RADIOLOGY_SCAN_TYPES.includes(scanType as RadiologyScanType)
}

export const isValidRadiologyStatus = (status: string): status is RadiologyStatus => {
  return RADIOLOGY_STATUSES.includes(status as RadiologyStatus)
}

export const isValidGenderType = (gender: string): gender is GenderType => {
  return GENDER_TYPES.includes(gender as GenderType)
}
