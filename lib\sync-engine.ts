"use client"

import { sqliteClient } from './sqlite'
import { supabase } from './supabase'
import { v4 as uuidv4 } from 'uuid'

// Table configuration for sync
export interface TableConfig {
  name: string
  primaryKey: string
  timestampField: string
  dirtyField: string
  deletedField: string
  excludeFromSync?: string[] // Fields to exclude from sync
}

// Sync result interface
export interface SyncResult {
  table: string
  pushed: number
  pulled: number
  conflicts: number
  errors: string[]
}

// Conflict resolution strategy
export type ConflictResolution = 'latest-wins' | 'local-wins' | 'remote-wins'

class SyncEngine {
  private isOnline = true
  private isSyncing = false
  private syncInterval: NodeJS.Timeout | null = null
  private isInitialized = false

  // Table configurations
  private tableConfigs: TableConfig[] = [
    {
      name: 'unit_types',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    },
    {
      name: 'patients',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    },
    {
      name: 'vital_signs',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    },
    {
      name: 'lab_values',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    },
    {
      name: 'medication_names',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    },
    {
      name: 'medications',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    },
    {
      name: 'medication_adherence',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    },
    {
      name: 'doctor_notes',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    },
    {
      name: 'cultures',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    },
    {
      name: 'radiology',
      primaryKey: 'id',
      timestampField: 'updated_at',
      dirtyField: 'dirty',
      deletedField: 'deleted'
    }
  ]

  constructor() {
    if (typeof window !== 'undefined') {
      this.initialize()
    }
  }

  private initialize() {
    if (this.isInitialized) return
    this.isInitialized = true

    this.isOnline = navigator.onLine

    window.addEventListener('online', () => {
      this.isOnline = true
      this.startPeriodicSync()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
      this.stopPeriodicSync()
    })

    if (this.isOnline) {
      this.startPeriodicSync()
    }
  }

  startPeriodicSync() {
    if (this.syncInterval) return

    this.syncInterval = setInterval(() => {
      this.syncAllTables()
    }, 5000) // 5 seconds

    // Initial sync
    this.syncAllTables()
  }

  stopPeriodicSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
    }
  }

  async syncAllTables(): Promise<SyncResult[]> {
    if (!this.isOnline || this.isSyncing) return []
    
    this.isSyncing = true
    const results: SyncResult[] = []

    try {
      for (const config of this.tableConfigs) {
        try {
          const result = await this.syncTable(config)
          results.push(result)
        } catch (error) {
          console.error(`Error syncing table ${config.name}:`, error)
          results.push({
            table: config.name,
            pushed: 0,
            pulled: 0,
            conflicts: 0,
            errors: [error instanceof Error ? error.message : 'Unknown error']
          })
        }
      }
    } finally {
      this.isSyncing = false
    }

    return results
  }

  async syncTable(config: TableConfig, conflictResolution: ConflictResolution = 'latest-wins'): Promise<SyncResult> {
    const result: SyncResult = {
      table: config.name,
      pushed: 0,
      pulled: 0,
      conflicts: 0,
      errors: []
    }

    try {
      // Push phase: Send dirty local records to remote
      const pushResult = await this.pushDirtyRecords(config)
      result.pushed = pushResult.count
      result.errors.push(...pushResult.errors)

      // Pull phase: Fetch updated remote records
      const pullResult = await this.pullRemoteRecords(config, conflictResolution)
      result.pulled = pullResult.count
      result.conflicts = pullResult.conflicts
      result.errors.push(...pullResult.errors)

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown sync error'
      result.errors.push(errorMsg)
      console.error(`Sync error for table ${config.name}:`, error)
    }

    return result
  }

  private async pushDirtyRecords(config: TableConfig): Promise<{ count: number; errors: string[] }> {
    const errors: string[] = []
    let count = 0

    try {
      // Get all dirty records
      const dirtyRecords = await sqliteClient.execute(
        `SELECT * FROM ${config.name} WHERE ${config.dirtyField} = 1`
      )

      for (const record of dirtyRecords) {
        try {
          // Prepare record for remote sync (exclude sync-specific fields)
          const syncRecord = this.prepareSyncRecord(record, config)

          if (record[config.deletedField]) {
            // Handle soft delete
            const { error } = await supabase
              .from(config.name)
              .delete()
              .eq(config.primaryKey, record[config.primaryKey])

            if (error) throw error
          } else {
            // Upsert record
            const { error } = await supabase
              .from(config.name)
              .upsert(syncRecord)

            if (error) throw error
          }

          // Mark as clean in local database
          await sqliteClient.run(
            `UPDATE ${config.name} SET ${config.dirtyField} = 0 WHERE ${config.primaryKey} = ?`,
            [record[config.primaryKey]]
          )

          count++
        } catch (error) {
          const errorMsg = `Failed to push record ${record[config.primaryKey]}: ${error instanceof Error ? error.message : 'Unknown error'}`
          errors.push(errorMsg)
          console.error(errorMsg, error)
        }
      }
    } catch (error) {
      const errorMsg = `Failed to fetch dirty records for ${config.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
      errors.push(errorMsg)
    }

    return { count, errors }
  }

  private async pullRemoteRecords(config: TableConfig, conflictResolution: ConflictResolution): Promise<{ count: number; conflicts: number; errors: string[] }> {
    const errors: string[] = []
    let count = 0
    let conflicts = 0

    try {
      // Get the latest timestamp from local records
      const lastSyncResult = await sqliteClient.execute(
        `SELECT MAX(${config.timestampField}) as last_sync FROM ${config.name}`
      )
      
      const lastSync = lastSyncResult[0]?.last_sync || '1970-01-01T00:00:00.000Z'

      // Fetch records updated since last sync
      const { data: remoteRecords, error } = await supabase
        .from(config.name)
        .select('*')
        .gt(config.timestampField, lastSync)
        .order(config.timestampField, { ascending: true })

      if (error) throw error

      if (!remoteRecords || remoteRecords.length === 0) {
        return { count, conflicts, errors }
      }

      for (const remoteRecord of remoteRecords) {
        try {
          // Check if record exists locally
          const localRecords = await sqliteClient.execute(
            `SELECT * FROM ${config.name} WHERE ${config.primaryKey} = ?`,
            [remoteRecord[config.primaryKey]]
          )

          const localRecord = localRecords[0]

          if (localRecord && localRecord[config.dirtyField]) {
            // Conflict: local record is dirty and remote record is newer
            conflicts++
            
            if (conflictResolution === 'latest-wins') {
              const localTime = new Date(localRecord[config.timestampField]).getTime()
              const remoteTime = new Date(remoteRecord[config.timestampField]).getTime()
              
              if (remoteTime > localTime) {
                await this.upsertLocalRecord(config, remoteRecord)
                count++
              }
            } else if (conflictResolution === 'remote-wins') {
              await this.upsertLocalRecord(config, remoteRecord)
              count++
            }
            // For 'local-wins', we skip the remote record
          } else {
            // No conflict, safe to update
            await this.upsertLocalRecord(config, remoteRecord)
            count++
          }
        } catch (error) {
          const errorMsg = `Failed to process remote record ${remoteRecord[config.primaryKey]}: ${error instanceof Error ? error.message : 'Unknown error'}`
          errors.push(errorMsg)
          console.error(errorMsg, error)
        }
      }
    } catch (error) {
      const errorMsg = `Failed to pull remote records for ${config.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
      errors.push(errorMsg)
    }

    return { count, conflicts, errors }
  }

  private prepareSyncRecord(record: any, config: TableConfig): any {
    const syncRecord = { ...record }
    
    // Remove sync-specific fields
    delete syncRecord[config.dirtyField]
    delete syncRecord[config.deletedField]
    
    // Remove any excluded fields
    if (config.excludeFromSync) {
      config.excludeFromSync.forEach(field => {
        delete syncRecord[field]
      })
    }

    return syncRecord
  }

  private async upsertLocalRecord(config: TableConfig, record: any): Promise<void> {
    // Prepare record for local storage
    const localRecord = {
      ...record,
      [config.dirtyField]: 0, // Mark as clean
      [config.deletedField]: 0 // Mark as not deleted
    }

    // Build upsert query
    const fields = Object.keys(localRecord)
    const placeholders = fields.map(() => '?').join(', ')
    const updateClause = fields.map(field => `${field} = ?`).join(', ')
    
    const sql = `
      INSERT INTO ${config.name} (${fields.join(', ')}) 
      VALUES (${placeholders})
      ON CONFLICT(${config.primaryKey}) DO UPDATE SET ${updateClause}
    `
    
    const values = [...Object.values(localRecord), ...Object.values(localRecord)]
    
    await sqliteClient.run(sql, values)
  }

  // Manual sync trigger
  async manualSync(): Promise<SyncResult[]> {
    if (!this.isOnline) {
      throw new Error('Cannot sync while offline')
    }
    
    return await this.syncAllTables()
  }

  // Get sync status
  async getSyncStatus(): Promise<{ pendingChanges: number; lastSync: string | null }> {
    let pendingChanges = 0
    let lastSync: string | null = null

    try {
      for (const config of this.tableConfigs) {
        const dirtyCount = await sqliteClient.execute(
          `SELECT COUNT(*) as count FROM ${config.name} WHERE ${config.dirtyField} = 1`
        )
        pendingChanges += dirtyCount[0]?.count || 0

        const lastSyncResult = await sqliteClient.execute(
          `SELECT MAX(${config.timestampField}) as last_sync FROM ${config.name}`
        )
        const tableLastSync = lastSyncResult[0]?.last_sync
        if (tableLastSync && (!lastSync || tableLastSync > lastSync)) {
          lastSync = tableLastSync
        }
      }
    } catch (error) {
      console.error('Error getting sync status:', error)
    }

    return { pendingChanges, lastSync }
  }

  // Get current sync state
  getSyncState(): { isOnline: boolean; isSyncing: boolean; isInitialized: boolean } {
    return {
      isOnline: this.isOnline,
      isSyncing: this.isSyncing,
      isInitialized: this.isInitialized
    }
  }

  // Get detailed sync status with per-table breakdown
  async getDetailedSyncStatus(): Promise<{
    isOnline: boolean;
    isSyncing: boolean;
    pendingChanges: number;
    lastSync: string | null;
    tableStatus: Array<{
      table: string;
      pendingChanges: number;
      lastSync: string | null;
    }>;
  }> {
    const state = this.getSyncState()
    let totalPendingChanges = 0
    let globalLastSync: string | null = null
    const tableStatus: Array<{
      table: string;
      pendingChanges: number;
      lastSync: string | null;
    }> = []

    try {
      for (const config of this.tableConfigs) {
        const dirtyCount = await sqliteClient.execute(
          `SELECT COUNT(*) as count FROM ${config.name} WHERE ${config.dirtyField} = 1`
        )
        const pendingChanges = dirtyCount[0]?.count || 0
        totalPendingChanges += pendingChanges

        const lastSyncResult = await sqliteClient.execute(
          `SELECT MAX(${config.timestampField}) as last_sync FROM ${config.name}`
        )
        const tableLastSync = lastSyncResult[0]?.last_sync

        if (tableLastSync && (!globalLastSync || tableLastSync > globalLastSync)) {
          globalLastSync = tableLastSync
        }

        tableStatus.push({
          table: config.name,
          pendingChanges,
          lastSync: tableLastSync
        })
      }
    } catch (error) {
      console.error('Error getting detailed sync status:', error)
    }

    return {
      isOnline: state.isOnline,
      isSyncing: state.isSyncing,
      pendingChanges: totalPendingChanges,
      lastSync: globalLastSync,
      tableStatus
    }
  }

  // Cleanup deleted records
  async cleanupDeletedRecords(): Promise<number> {
    let deletedCount = 0

    try {
      for (const config of this.tableConfigs) {
        const result = await sqliteClient.execute(
          `DELETE FROM ${config.name} WHERE ${config.deletedField} = 1 AND ${config.dirtyField} = 0`
        )
        // Note: sql.js doesn't return affected rows count directly
        // We could implement a count query before deletion if needed
      }
    } catch (error) {
      console.error('Error cleaning up deleted records:', error)
    }

    return deletedCount
  }
}

export const syncEngine = new SyncEngine()
