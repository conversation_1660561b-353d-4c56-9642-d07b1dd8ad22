"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { getSession } from "@/lib/auth"
import { AppHeader } from "./app-header"

interface ProtectedLayoutProps {
  children: React.ReactNode
  requireAdmin?: boolean
}

export function ProtectedLayout({ children, requireAdmin = false }: ProtectedLayoutProps) {
  const router = useRouter()
  const [session, setSession] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const currentSession = getSession()
    setSession(currentSession)

    if (!currentSession) {
      router.push("/login")
      return
    }

    if (requireAdmin && currentSession.user.role !== "admin") {
      router.push("/")
      return
    }

    setIsLoading(false)
  }, [router, requireAdmin])

  // Show loading state while checking session
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">Loading...</div>
      </div>
    )
  }

  // Don't render anything if no session or insufficient permissions
  if (!session) return null
  if (requireAdmin && session.user.role !== "admin") return null

  return (
    <div className="min-h-screen bg-background">
      <AppHeader />
      <main className="container mx-auto px-4 py-6">{children}</main>
    </div>
  )
}
