"use client"

import type React from "react"
import { useState, useEffect, useMemo, useCallback } from "react"
import { Plus, Search, Trash2, Calendar, AlertCircle, XCircle, Edit } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { Badge } from "@/components/ui/badge"
import { dataClient } from "@/lib/data-client"
import type { Medication, MedicationAdherence } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface MedicationsSectionProps {
  patientId: string
}

interface SavingState {
  [key: string]: boolean // key: `${medicationId}|${date}`
}

interface ErrorState {
  [key: string]: string // key: `${medicationId}|${date}` -> error message
}

export function MedicationsSection({ patientId }: MedicationsSectionProps) {
  const [medications, setMedications] = useState<Medication[]>([])
  const [medicationNames, setMedicationNames] = useState<string[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [newMedicationName, setNewMedicationName] = useState("")
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split("T")[0],
    medication_name: "",
    dosage: "",
    frequency: "",
  })
  const { toast } = useToast()

  // Grid state management
  const [adherenceMap, setAdherenceMap] = useState<{ [key: string]: boolean }>({})
  const [savingKeys, setSavingKeys] = useState<SavingState>({})
  const [errorKeys, setErrorKeys] = useState<ErrorState>({})
  const [isLoading, setIsLoading] = useState(true)
  const [lastError, setLastError] = useState<string | null>(null)

  // Calculate dynamic date range
  const dateRange = useMemo(() => {
    if (medications.length === 0) {
      // If no medications, show last 7 days + today + next 3 days
      const today = new Date()
      const dates: string[] = []
      
      // Last 7 days
      for (let i = 6; i >= 1; i--) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)
        dates.push(date.toISOString().split("T")[0])
      }
      
      // Today
      dates.push(today.toISOString().split("T")[0])
      
      // Next 3 days
      for (let i = 1; i <= 3; i++) {
        const date = new Date(today)
        date.setDate(date.getDate() + i)
        dates.push(date.toISOString().split("T")[0])
      }
      
      return dates
    }

    // Calculate date range from medication data and adherence
    const allDates = new Set<string>()
    
    // Add medication prescribed dates
    medications.forEach(med => {
      allDates.add(med.date_prescribed)
    })
    
    // Add dates from adherence data
    Object.keys(adherenceMap).forEach(key => {
      const [, date] = key.split('|')
      if (date) allDates.add(date)
    })
    
    // Add today and a few days around
    const today = new Date()
    allDates.add(today.toISOString().split("T")[0])
    
    // Add some future dates for planning
    for (let i = 1; i <= 7; i++) {
      const futureDate = new Date(today)
      futureDate.setDate(today.getDate() + i)
      allDates.add(futureDate.toISOString().split("T")[0])
    }
    
    // Convert to array and sort chronologically
    const sortedDates = Array.from(allDates).sort((a, b) => 
      new Date(a).getTime() - new Date(b).getTime()
    )
    
    return sortedDates
  }, [medications, adherenceMap])

  useEffect(() => {
    loadData()
  }, [patientId])

  const loadData = async () => {
    setIsLoading(true)
    setLastError(null)
    
    try {
      await Promise.all([
        loadMedications(),
        loadMedicationNames()
      ])
    } catch (error) {
      console.error("Error loading data:", error)
      setLastError("Failed to load medication data. Please refresh the page.")
      toast({
        title: "Error",
        description: "Failed to load medication data.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const loadMedications = async () => {
    try {
      const medicationsData = await dataClient.getMedications(patientId)
      setMedications(medicationsData)
      
      // Load adherence data
      await loadAdherenceData(medicationsData)
    } catch (error) {
      console.error("Error loading medications:", error)
      throw error
    }
  }

  const loadAdherenceData = async (meds: Medication[]) => {
    try {
      const adherence = await dataClient.getMedicationAdherenceByPatient(patientId)
      const map: { [key: string]: boolean } = {}
      
      adherence.forEach(row => {
        map[`${row.medication_id}|${row.date}`] = row.is_taking_medication
      })
      
      setAdherenceMap(map)
    } catch (error) {
      console.error("Error loading adherence data:", error)
      throw error
    }
  }

  const loadMedicationNames = async () => {
    try {
      const names = await dataClient.getMedicationNames()
      setMedicationNames(names)
    } catch (error) {
      console.error("Error loading medication names:", error)
      throw error
    }
  }

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split("T")[0],
      medication_name: "",
      dosage: "",
      frequency: "",
    })
    setSearchTerm("")
    setNewMedicationName("")
  }

  const filteredMedicationNames = medicationNames.filter((name) =>
    name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddNewMedication = async () => {
    if (!newMedicationName.trim()) return

    try {
      await dataClient.insertMedicationName(newMedicationName.trim())
      await loadMedicationNames()
      setFormData({ ...formData, medication_name: newMedicationName.trim() })
      setNewMedicationName("")
      toast({
        title: "Medication added",
        description: "New medication has been added to the catalog.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add new medication to catalog.",
        variant: "destructive",
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.medication_name) {
      toast({
        title: "Error",
        description: "Please select or add a medication name.",
        variant: "destructive",
      })
      return
    }

    try {
      await dataClient.insertMedication({
        patient_id: patientId,
        medication_name: formData.medication_name,
        dosage: formData.dosage,
        frequency: formData.frequency,
        date_prescribed: formData.date,
        is_active: true,
      })

      await loadMedications()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: "Medication added",
        description: "Medication has been successfully added.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add medication.",
        variant: "destructive",
      })
    }
  }

  const handleToggleActive = async (medication: Medication) => {
    try {
      await dataClient.updateMedication(medication.id, {
        is_active: !medication.is_active,
      })
      await loadMedications()

      toast({
        title: "Medication updated",
        description: `Medication ${medication.is_active ? "deactivated" : "activated"}.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update medication status.",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (medication: Medication) => {
    try {
      await dataClient.deleteMedication(medication.id)
      await loadMedications()

      toast({
        title: "Medication deleted",
        description: "Medication has been removed.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete medication.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const isToday = date.toDateString() === today.toDateString()
    const isPast = date < today
    
    let formatted = date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
    
    if (isToday) {
      formatted += ' (Today)'
    } else if (isPast) {
      formatted += ' (Past)'
    }
    
    return formatted
  }

  const getDateColumnClass = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const isToday = date.toDateString() === today.toDateString()
    const isPast = date < today
    
    if (isToday) return "bg-blue-50 border-l-2 border-l-blue-500"
    if (isPast) return "bg-gray-50"
    return "bg-green-50"
  }

  const handleToggleAdherence = useCallback(async (
    medicationId: string, 
    dateIso: string, 
    nextValue: boolean
  ) => {
    const key = `${medicationId}|${dateIso}`
    
    // Clear any previous errors for this key
    setErrorKeys(prev => {
      const newErrors = { ...prev }
      delete newErrors[key]
      return newErrors
    })
    
    // Optimistic update
    setSavingKeys(prev => ({ ...prev, [key]: true }))
    setAdherenceMap(prev => ({ ...prev, [key]: nextValue }))
    
    try {
      await dataClient.upsertMedicationAdherence({
        patient_id: patientId,
        medication_id: medicationId,
        date: dateIso,
        is_taking_medication: nextValue,
      })
      
      toast({
        title: "Updated",
        description: `Medication adherence updated for ${formatDate(dateIso)}`,
      })
    } catch (error) {
      console.error("Error updating adherence:", error)
      
      // Revert optimistic update
      setAdherenceMap(prev => ({ ...prev, [key]: !nextValue }))
      
      // Set error state
      setErrorKeys(prev => ({ 
        ...prev, 
        [key]: "Failed to save. Please try again." 
      }))
      
      toast({
        title: "Error",
        description: "Failed to save medication adherence. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSavingKeys(prev => ({ ...prev, [key]: false }))
    }
  }, [patientId, toast])

  const addNewDateColumn = () => {
    // Find the latest date and add one more day
    const latestDate = dateRange[dateRange.length - 1]
    if (latestDate) {
      const nextDate = new Date(latestDate)
      nextDate.setDate(nextDate.getDate() + 1)
      const newDateString = nextDate.toISOString().split("T")[0]
      
      // Force re-render by updating medications (this will trigger dateRange recalculation)
      setMedications(prev => [...prev])
    }
  }

  const getAdherenceStatus = (medicationId: string, date: string) => {
    const key = `${medicationId}|${date}`
    const isTaking = adherenceMap[key]
    const isSaving = savingKeys[key]
    const hasError = errorKeys[key]
    
    return { isTaking, isSaving, hasError }
  }



  // Separate active and inactive medications
  const activeMedications = medications.filter(med => med.is_active)
  const inactiveMedications = medications.filter(med => !med.is_active)

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Medications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-2">Loading medications...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Medications Tracking Grid
        </CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" onClick={addNewDateColumn}>
            <Plus className="h-4 w-4 mr-2" />
            Add Day
          </Button>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Add Medication
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-lg">
              <DialogHeader>
                <DialogTitle>Add Medication</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="date">Date Prescribed</Label>
                  <Input
                    id="date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>Search existing medication</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search and select medication"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  {searchTerm && filteredMedicationNames.length > 0 && (
                    <div className="border rounded-md max-h-32 overflow-y-auto">
                      {filteredMedicationNames.slice(0, 5).map((name) => (
                        <div
                          key={name}
                          className="p-2 hover:bg-muted cursor-pointer"
                          onClick={() => {
                            setFormData({ ...formData, medication_name: name })
                            setSearchTerm("")
                          }}
                        >
                          {name}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label><strong>Not There?</strong> Add New</Label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter a new medication name"
                      value={newMedicationName}
                      onChange={(e) => setNewMedicationName(e.target.value)}
                    />
                    <Button type="button" onClick={handleAddNewMedication}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {formData.medication_name && (
                  <div className="p-2 bg-muted rounded">
                    Selected: <strong>{formData.medication_name}</strong>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="dosage">Dosage</Label>
                  <Input
                    id="dosage"
                    placeholder="e.g., 500mg"
                    value={formData.dosage}
                    onChange={(e) => setFormData({ ...formData, dosage: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="frequency">Frequency</Label>
                  <Select onValueChange={(value) => setFormData({ ...formData, frequency: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="every 24 hours">every 24 hours</SelectItem>
                      <SelectItem value="every 12 hours">every 12 hours</SelectItem>
                      <SelectItem value="every 8 hours">every 8 hours</SelectItem>
                      <SelectItem value="every 6 hours">every 6 hours</SelectItem>
                      <SelectItem value="every 4 hours">every 4 hours</SelectItem>
                      <SelectItem value="every 2 hours">every 2 hours</SelectItem>
                      <SelectItem value="As Needed">As Needed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Add Medication</Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      {lastError && (
        <div className="px-6 pb-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{lastError}</AlertDescription>
          </Alert>
        </div>
      )}

      <CardContent className="p-0">
        {medications.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No medications recorded yet. Add your first medication to start tracking adherence.
          </div>
        ) : (
          <div className="relative">
            {/* Table container with horizontal scroll */}
            <div className="overflow-x-auto">
              <div className="min-w-fit">
                {/* Header row */}
                <div className="flex border-b bg-background sticky top-0 z-20">
                  {/* Sticky first column header */}
                  <div className="sticky left-0 z-30 w-48 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                    <div className="h-16 px-4 flex items-center font-semibold">
                      Medication
                    </div>
                  </div>

                  {/* Date column headers - fixed width */}
                  <div className="flex">
                    {dateRange.map((date) => (
                      <div key={date} className={`w-20 flex-shrink-0 border-r border-border last:border-r-0 ${getDateColumnClass(date)}`}>
                        <div className="h-16 px-2 flex flex-col items-center justify-center">
                          <div className="font-semibold text-xs text-center">
                            {formatDate(date)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Actions column header */}
                  <div className="w-32 flex-shrink-0 bg-[#f1f5f9] border-l border-border">
                    <div className="h-16 px-2 flex items-center justify-center font-semibold">
                      Actions
                    </div>
                  </div>
                </div>
                {/* Table body */}
                <div className="divide-y divide-border">
                  {/* Active Medications */}
                  {activeMedications.map((medication) => (
                    <div key={medication.id} className="flex bg-green-100 hover:bg-green-50/70 transition-colors">
                      {/* Sticky first column */}
                      <div className="sticky left-0 z-30 w-48 flex-shrink-0 bg-green-100 border-r border-border">
                        <div className="px-2  flex flex-col justify-center min-h-[60px]">
                          <span className="font-semibold text-sm">{medication.medication_name}</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {medication.dosage && <Badge variant="secondary" className="text-xs">{medication.dosage}</Badge>}
                            {medication.frequency && <Badge variant="secondary" className="text-xs">{medication.frequency}</Badge>}
                          </div>
                        </div>
                      </div>

                      {/* Date columns - fixed width */}
                      <div className="flex">
                        {dateRange.map((date) => {
                          const { isTaking, isSaving, hasError } = getAdherenceStatus(medication.id, date)
                          const key = `${medication.id}|${date}`

                          return (
                            <div key={key} className={`w-20 flex-shrink-0 border-r border-border last:border-r-0 ${getDateColumnClass(date)}`}>
                              <div className="px-2  flex items-center justify-center min-h-[60px]">
                                <div className="flex flex-col items-center gap-1">
                                  <Checkbox
                                    checked={isTaking}
                                    disabled={isSaving}
                                    onCheckedChange={(v) =>
                                      handleToggleAdherence(medication.id, date, v === true)
                                    }
                                    className={hasError ? "border-red-500" : ""}
                                  />
                                  {isSaving && (
                                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                                  )}
                                  {hasError && (
                                    <XCircle className="w-3 h-3 text-red-500" />
                                  )}
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>

                      {/* Actions column */}
                      <div className="w-32 flex-shrink-0 bg-green-100 border-l border-border">
                        <div className="px-2  flex items-center justify-center min-h-[60px]">
                          <div className="flex flex-row items-center gap-1">
                            <Badge
                              variant="default"
                              className="cursor-pointer bg-green-600 hover:bg-green-700 text-xs"
                              onClick={() => handleToggleActive(medication)}
                            >
                              Active
                            </Badge>
                            <Button variant="ghost" size="sm" onClick={() => handleDelete(medication)}>
                              <Trash2 className="h-3 w-3 text-red-600" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Inactive Medications */}
                  {inactiveMedications.map((medication) => (
                    <div key={medication.id} className="flex bg-red-100 hover:bg-red-50/70 transition-colors">
                      {/* Sticky first column */}
                      <div className="sticky left-0 z-30 w-48 flex-shrink-0 bg-red-100 border-r border-border">
                        <div className="px-2  flex flex-col justify-center min-h-[60px]">
                          <span className="text-muted-foreground text-sm">{medication.medication_name}</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {medication.dosage && <Badge variant="secondary" className="text-xs">{medication.dosage}</Badge>}
                            {medication.frequency && <Badge variant="secondary" className="text-xs">{medication.frequency}</Badge>}
                          </div>
                        </div>
                      </div>

                      {/* Date columns - fixed width */}
                      <div className="flex">
                        {dateRange.map((date) => {
                          const { isTaking, isSaving, hasError } = getAdherenceStatus(medication.id, date)
                          const key = `${medication.id}|${date}`

                          return (
                            <div key={key} className={`w-20 flex-shrink-0 border-r border-border last:border-r-0 ${getDateColumnClass(date)}`}>
                              <div className="px-2  flex items-center justify-center min-h-[60px]">
                                <div className="flex flex-col items-center gap-1">
                                  <Checkbox
                                    checked={isTaking}
                                    disabled={isSaving}
                                    onCheckedChange={(v) =>
                                      handleToggleAdherence(medication.id, date, v === true)
                                    }
                                    className={hasError ? "border-red-500" : ""}
                                  />
                                  {isSaving && (
                                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                                  )}
                                  {hasError && (
                                    <XCircle className="w-3 h-3 text-red-500" />
                                  )}
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>

                      {/* Actions column */}
                      <div className="w-32 flex-shrink-0 bg-red-100 border-l border-border">
                        <div className="px-2  flex items-center justify-center min-h-[60px]">
                          <div className="flex flex-row items-center gap-1">
                            <Badge
                              variant="default"
                              className="cursor-pointer bg-red-600 hover:bg-red-700 text-xs"
                              onClick={() => handleToggleActive(medication)}
                            >
                              Inactive
                            </Badge>
                            <Button variant="ghost" size="sm" onClick={() => handleDelete(medication)}>
                              <Trash2 className="h-3 w-3 text-red-600" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
