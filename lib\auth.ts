import { supabase } from "./supabase"
import type { User } from "./types"

const SESSION_KEY = "icu_session"
const SESSION_TTL = 8 * 60 * 60 * 1000 // 8 hours

export interface Session {
  user: User
  expiresAt: number
}

export function getSession(): Session | null {
  if (typeof window === "undefined") return null

  const sessionData = localStorage.getItem(SESSION_KEY)
  if (!sessionData) return null

  try {
    const session: Session = JSON.parse(sessionData)
    if (Date.now() > session.expiresAt) {
      localStorage.removeItem(SESSION_KEY)
      return null
    }
    return session
  } catch {
    localStorage.removeItem(SESSION_KEY)
    return null
  }
}

export function setSession(user: User) {
  const session: Session = {
    user,
    expiresAt: Date.now() + SESSION_TTL,
  }
  localStorage.setItem(SESSION_KEY, JSON.stringify(session))
}

export function clearSession() {
  localStorage.removeItem(SESSION_KEY)
}

export async function login(
  username: string,
  password: string,
): Promise<{ success: boolean; user?: User; message?: string }> {
  try {
    const { data, error } = await supabase.rpc("login", {
      username_input: username,
      password_input: password,
    })

    if (error) throw error

    if (data.success) {
      setSession(data.user)
      return { success: true, user: data.user }
    } else {
      return { success: false, message: data.message }
    }
  } catch (error) {
    return { success: false, message: "Login failed" }
  }
}

export function logout() {
  clearSession()
}
