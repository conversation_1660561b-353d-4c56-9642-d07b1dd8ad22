"use client"

import initSqlJs, { Database, SqlJsStatic } from 'sql.js'

class SQLiteClient {
  private SQL: SqlJsStatic | null = null
  private db: Database | null = null
  private isInitialized = false
  private initPromise: Promise<void> | null = null

  constructor() {
    if (typeof window !== 'undefined') {
      this.initPromise = this.initialize()
    }
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) return
    
    try {
      // Initialize sql.js
      this.SQL = await initSqlJs({
        locateFile: (file) => `https://sql.js.org/dist/${file}`
      })

      // Try to load existing database from localStorage
      const savedDb = localStorage.getItem('sqlite-db')
      if (savedDb) {
        const uint8Array = new Uint8Array(JSON.parse(savedDb))
        this.db = new this.SQL.Database(uint8Array)
      } else {
        // Create new database
        this.db = new this.SQL.Database()
      }

      // Create tables with standardized sync columns
      await this.createTables()
      
      this.isInitialized = true
      console.log('SQLite database initialized successfully')
    } catch (error) {
      console.error('Failed to initialize SQLite:', error)
      throw error
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    const tables = [
      // Unit types (reference table)
      `CREATE TABLE IF NOT EXISTS unit_types (
        id INTEGER PRIMARY KEY,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE
      )`,

      // Patients table
      `CREATE TABLE IF NOT EXISTS patients (
        id TEXT PRIMARY KEY,
        patient_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        gender TEXT NOT NULL,
        age INTEGER CHECK (age >= 1 AND age <= 150),
        weight REAL CHECK (weight > 0),
        admission_date TEXT DEFAULT (date('now')),
        unit_id INTEGER REFERENCES unit_types(id),
        main_complaint TEXT,
        medical_history TEXT,
        initial_diagnosis TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        is_discharged BOOLEAN DEFAULT FALSE,
        is_deceased BOOLEAN DEFAULT FALSE,
        diseases TEXT DEFAULT '[]', -- JSON string
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE
      )`,

      // Vital signs table
      `CREATE TABLE IF NOT EXISTS vital_signs (
        id TEXT PRIMARY KEY,
        patient_id TEXT NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
        date TEXT NOT NULL,
        vital_signs_data TEXT DEFAULT '{}', -- JSON string
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE,
        UNIQUE(patient_id, date)
      )`,

      // Lab values table
      `CREATE TABLE IF NOT EXISTS lab_values (
        id TEXT PRIMARY KEY,
        patient_id TEXT NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
        date TEXT NOT NULL,
        lab_data TEXT DEFAULT '{}', -- JSON string
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE,
        UNIQUE(patient_id, date)
      )`,

      // Medication names catalog
      `CREATE TABLE IF NOT EXISTS medication_names (
        id TEXT PRIMARY KEY,
        name TEXT UNIQUE NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE
      )`,

      // Medications table
      `CREATE TABLE IF NOT EXISTS medications (
        id TEXT PRIMARY KEY,
        patient_id TEXT NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
        medication_name TEXT NOT NULL,
        dosage TEXT,
        frequency TEXT,
        date_prescribed TEXT DEFAULT (date('now')),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE
      )`,

      // Medication adherence table
      `CREATE TABLE IF NOT EXISTS medication_adherence (
        id TEXT PRIMARY KEY,
        patient_id TEXT NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
        medication_id TEXT NOT NULL REFERENCES medications(id) ON DELETE CASCADE,
        date TEXT NOT NULL,
        is_taking_medication BOOLEAN DEFAULT FALSE,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE,
        UNIQUE(patient_id, medication_id, date)
      )`,

      // Doctor notes table
      `CREATE TABLE IF NOT EXISTS doctor_notes (
        id TEXT PRIMARY KEY,
        patient_id TEXT NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
        date TEXT DEFAULT (date('now')),
        content TEXT NOT NULL,
        doctor_name TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE
      )`,

      // Cultures table
      `CREATE TABLE IF NOT EXISTS cultures (
        id TEXT PRIMARY KEY,
        patient_id TEXT NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
        requested_date TEXT NOT NULL,
        result_date TEXT,
        culture_type TEXT,
        results TEXT,
        microorganism TEXT,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE
      )`,

      // Radiology table
      `CREATE TABLE IF NOT EXISTS radiology (
        id TEXT PRIMARY KEY,
        patient_id TEXT NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
        scan_type TEXT NOT NULL,
        scan_date TEXT NOT NULL,
        body_part TEXT,
        findings TEXT,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        dirty BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE
      )`
    ]

    // Create indexes for better performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_patients_patient_id ON patients(patient_id)',
      'CREATE INDEX IF NOT EXISTS idx_patients_unit_id ON patients(unit_id)',
      'CREATE INDEX IF NOT EXISTS idx_patients_dirty ON patients(dirty)',
      'CREATE INDEX IF NOT EXISTS idx_patients_deleted ON patients(deleted)',
      'CREATE INDEX IF NOT EXISTS idx_vital_signs_patient_date ON vital_signs(patient_id, date)',
      'CREATE INDEX IF NOT EXISTS idx_vital_signs_dirty ON vital_signs(dirty)',
      'CREATE INDEX IF NOT EXISTS idx_lab_values_patient_date ON lab_values(patient_id, date)',
      'CREATE INDEX IF NOT EXISTS idx_lab_values_dirty ON lab_values(dirty)',
      'CREATE INDEX IF NOT EXISTS idx_medications_patient_id ON medications(patient_id)',
      'CREATE INDEX IF NOT EXISTS idx_medications_dirty ON medications(dirty)',
      'CREATE INDEX IF NOT EXISTS idx_medication_adherence_patient_med_date ON medication_adherence(patient_id, medication_id, date)',
      'CREATE INDEX IF NOT EXISTS idx_medication_adherence_dirty ON medication_adherence(dirty)',
      'CREATE INDEX IF NOT EXISTS idx_doctor_notes_patient_id ON doctor_notes(patient_id)',
      'CREATE INDEX IF NOT EXISTS idx_doctor_notes_dirty ON doctor_notes(dirty)',
      'CREATE INDEX IF NOT EXISTS idx_cultures_patient_id ON cultures(patient_id)',
      'CREATE INDEX IF NOT EXISTS idx_cultures_dirty ON cultures(dirty)',
      'CREATE INDEX IF NOT EXISTS idx_radiology_patient_id ON radiology(patient_id)',
      'CREATE INDEX IF NOT EXISTS idx_radiology_dirty ON radiology(dirty)'
    ]

    try {
      // Create all tables
      for (const tableSQL of tables) {
        this.db.run(tableSQL)
      }

      // Create all indexes
      for (const indexSQL of indexes) {
        this.db.run(indexSQL)
      }

      console.log('SQLite tables and indexes created successfully')
    } catch (error) {
      console.error('Error creating tables:', error)
      throw error
    }
  }

  async ensureInitialized(): Promise<void> {
    if (this.initPromise) {
      await this.initPromise
    }
    if (!this.isInitialized) {
      throw new Error('SQLite client not initialized')
    }
  }

  async execute(sql: string, params: any[] = []): Promise<any[]> {
    await this.ensureInitialized()
    if (!this.db) throw new Error('Database not available')

    try {
      const stmt = this.db.prepare(sql)
      const results: any[] = []
      
      while (stmt.step()) {
        const row = stmt.getAsObject()
        results.push(row)
      }
      
      stmt.free()
      return results
    } catch (error) {
      console.error('SQL execution error:', error, { sql, params })
      throw error
    }
  }

  async run(sql: string, params: any[] = []): Promise<void> {
    await this.ensureInitialized()
    if (!this.db) throw new Error('Database not available')

    try {
      this.db.run(sql, params)
      this.saveToLocalStorage()
    } catch (error) {
      console.error('SQL run error:', error, { sql, params })
      throw error
    }
  }

  async transaction<T>(callback: () => Promise<T>): Promise<T> {
    await this.ensureInitialized()
    if (!this.db) throw new Error('Database not available')

    this.db.run('BEGIN TRANSACTION')
    try {
      const result = await callback()
      this.db.run('COMMIT')
      this.saveToLocalStorage()
      return result
    } catch (error) {
      this.db.run('ROLLBACK')
      throw error
    }
  }

  private saveToLocalStorage(): void {
    if (!this.db) return
    
    try {
      const data = this.db.export()
      const dataArray = Array.from(data)
      localStorage.setItem('sqlite-db', JSON.stringify(dataArray))
    } catch (error) {
      console.error('Error saving database to localStorage:', error)
    }
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close()
      this.db = null
    }
    this.isInitialized = false
  }

  // Helper method to get current timestamp
  getCurrentTimestamp(): string {
    return new Date().toISOString()
  }
}

export const sqliteClient = new SQLiteClient()
