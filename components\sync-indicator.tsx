"use client"

import { Badge } from '@/components/ui/badge'
import { useSyncState } from '@/hooks/use-sync-status'
import {
  Wifi,
  WifiOff,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface SyncIndicatorProps {
  refreshInterval?: number
  showText?: boolean
}

export function SyncIndicator({ refreshInterval = 3000, showText = true }: SyncIndicatorProps) {
  const { syncState, isLoading } = useSyncState(refreshInterval)

  const getStatusIcon = () => {
    if (isLoading) return <Loader2 className="h-3 w-3 animate-spin" />
    if (!syncState?.isOnline) return <WifiOff className="h-3 w-3" />
    if (syncState.isSyncing) return <RefreshCw className="h-3 w-3 animate-spin" />
    if (syncState.pendingChanges > 0) return <AlertCircle className="h-3 w-3" />
    return <CheckCircle className="h-3 w-3" />
  }

  const getStatusText = () => {
    if (isLoading) return "Loading..."
    if (!syncState?.isOnline) return "Offline"
    if (syncState.isSyncing) return "Syncing..."
    if (syncState.pendingChanges > 0) return `${syncState.pendingChanges} pending`
    return "Synced"
  }

  const getStatusVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    if (isLoading) return "secondary"
    if (!syncState?.isOnline) return "destructive"
    if (syncState.isSyncing) return "default"
    if (syncState.pendingChanges > 0) return "outline"
    return "default"
  }

  return (
    <Badge variant={getStatusVariant()} className="flex items-center gap-1">
      {getStatusIcon()}
      {showText && <span className="text-xs">{getStatusText()}</span>}
    </Badge>
  )
}
