"use client"

import { sqliteClient } from './sqlite'
import { v4 as uuidv4 } from 'uuid'
import type { 
  Patient, 
  VitalSigns, 
  LabValues, 
  Medication, 
  DoctorNote, 
  Culture, 
  Radiology, 
  MedicationAdherence, 
  UnitType,
  MedicationName 
} from './types'
import { GENDER_TYPES, CULTURE_STATUSES, RA<PERSON><PERSON>OGY_SCAN_TYPES, RADI<PERSON>OGY_STATUSES, USER_ROLES } from './constants'

class SQLiteDataClient {
  // Reference data methods (return hardcoded constants)
  getUserRoles() {
    return [...USER_ROLES]
  }

  getGenderTypes() {
    return [...GENDER_TYPES]
  }

  getCultureStatuses() {
    return [...CULTURE_STATUSES]
  }

  getRadiologyScanTypes() {
    return [...RADIOLOGY_SCAN_TYPES]
  }

  getRadiologyStatuses() {
    return [...RADIOLOGY_STATUSES]
  }

  // Unit Types
  async getUnitTypes(): Promise<UnitType[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM unit_types WHERE deleted = 0 ORDER BY name'
    )
    return results.map(this.transformUnitType)
  }

  async insertUnitType(unitType: Omit<UnitType, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<UnitType> {
    const now = sqliteClient.getCurrentTimestamp()
    const newUnitType: UnitType = {
      ...unitType,
      id: Date.now(), // Use timestamp for integer ID
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false
    }

    await sqliteClient.run(
      `INSERT INTO unit_types (id, name, description, is_active, created_at, updated_at, dirty, deleted) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [newUnitType.id, newUnitType.name, newUnitType.description || null, newUnitType.is_active, 
       newUnitType.created_at, newUnitType.updated_at, newUnitType.dirty ? 1 : 0, newUnitType.deleted ? 1 : 0]
    )

    return newUnitType
  }

  // Patients
  async getPatients(): Promise<Patient[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM patients WHERE deleted = 0 ORDER BY created_at DESC'
    )
    return results.map(this.transformPatient)
  }

  async getPatient(id: string): Promise<Patient | null> {
    const results = await sqliteClient.execute(
      'SELECT * FROM patients WHERE id = ? AND deleted = 0',
      [id]
    )
    return results.length > 0 ? this.transformPatient(results[0]) : null
  }

  async getPatientByPatientId(patientId: string): Promise<Patient | null> {
    const results = await sqliteClient.execute(
      'SELECT * FROM patients WHERE patient_id = ? AND deleted = 0',
      [patientId]
    )
    return results.length > 0 ? this.transformPatient(results[0]) : null
  }

  async insertPatient(patient: Omit<Patient, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<Patient> {
    const id = uuidv4()
    const now = sqliteClient.getCurrentTimestamp()
    const newPatient: Patient = {
      ...patient,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false
    }

    await sqliteClient.run(
      `INSERT INTO patients (id, patient_id, name, gender, age, weight, admission_date, unit_id, 
       main_complaint, medical_history, initial_diagnosis, is_active, is_discharged, is_deceased, 
       diseases, created_at, updated_at, dirty, deleted) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        newPatient.id, newPatient.patient_id, newPatient.name, newPatient.gender,
        newPatient.age, newPatient.weight, newPatient.admission_date, newPatient.unit_id || null,
        newPatient.main_complaint || null, newPatient.medical_history || null, 
        newPatient.initial_diagnosis || null, newPatient.is_active ? 1 : 0,
        newPatient.is_discharged ? 1 : 0, newPatient.is_deceased ? 1 : 0,
        JSON.stringify(newPatient.diseases), newPatient.created_at, newPatient.updated_at,
        newPatient.dirty ? 1 : 0, newPatient.deleted ? 1 : 0
      ]
    )

    return newPatient
  }

  async updatePatient(id: string, updates: Partial<Patient>): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    const updatedData = { ...updates, updated_at: now, dirty: true }

    const fields = Object.keys(updatedData)
    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => {
      const value = updatedData[field as keyof typeof updatedData]
      if (field === 'diseases' && Array.isArray(value)) {
        return JSON.stringify(value)
      }
      if (typeof value === 'boolean') {
        return value ? 1 : 0
      }
      return value
    })

    await sqliteClient.run(
      `UPDATE patients SET ${setClause} WHERE id = ?`,
      [...values, id]
    )
  }

  async deletePatient(id: string): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    
    await sqliteClient.transaction(async () => {
      // Get patient_id for cascading soft deletes
      const patientResults = await sqliteClient.execute(
        'SELECT patient_id FROM patients WHERE id = ?',
        [id]
      )
      
      if (patientResults.length === 0) return
      
      const patientId = patientResults[0].patient_id

      // Soft delete patient
      await sqliteClient.run(
        'UPDATE patients SET deleted = 1, dirty = 1, updated_at = ? WHERE id = ?',
        [now, id]
      )

      // Soft delete related records
      const relatedTables = [
        'vital_signs', 'lab_values', 'medications', 'medication_adherence',
        'doctor_notes', 'cultures', 'radiology'
      ]

      for (const table of relatedTables) {
        await sqliteClient.run(
          `UPDATE ${table} SET deleted = 1, dirty = 1, updated_at = ? WHERE patient_id = ?`,
          [now, patientId]
        )
      }
    })
  }

  // Vital Signs
  async getVitalSigns(patientId: string): Promise<VitalSigns[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM vital_signs WHERE patient_id = ? AND deleted = 0 ORDER BY date DESC',
      [patientId]
    )
    return results.map(this.transformVitalSigns)
  }

  async insertVitalSigns(vitalSigns: Omit<VitalSigns, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<VitalSigns> {
    const id = uuidv4()
    const now = sqliteClient.getCurrentTimestamp()
    const newVitalSigns: VitalSigns = {
      ...vitalSigns,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false
    }

    await sqliteClient.run(
      `INSERT INTO vital_signs (id, patient_id, date, vital_signs_data, created_at, updated_at, dirty, deleted) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        newVitalSigns.id, newVitalSigns.patient_id, newVitalSigns.date,
        JSON.stringify(newVitalSigns.vital_signs_data), newVitalSigns.created_at,
        newVitalSigns.updated_at, newVitalSigns.dirty ? 1 : 0, newVitalSigns.deleted ? 1 : 0
      ]
    )

    return newVitalSigns
  }

  async updateVitalSigns(id: string, updates: Partial<VitalSigns>): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    const updatedData = { ...updates, updated_at: now, dirty: true }

    const fields = Object.keys(updatedData)
    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => {
      const value = updatedData[field as keyof typeof updatedData]
      if (field === 'vital_signs_data' && typeof value === 'object') {
        return JSON.stringify(value)
      }
      if (typeof value === 'boolean') {
        return value ? 1 : 0
      }
      return value
    })

    await sqliteClient.run(
      `UPDATE vital_signs SET ${setClause} WHERE id = ?`,
      [...values, id]
    )
  }

  async deleteVitalSigns(id: string): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    await sqliteClient.run(
      'UPDATE vital_signs SET deleted = 1, dirty = 1, updated_at = ? WHERE id = ?',
      [now, id]
    )
  }

  // Lab Values
  async getLabValues(patientId: string): Promise<LabValues[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM lab_values WHERE patient_id = ? AND deleted = 0 ORDER BY date DESC',
      [patientId]
    )
    return results.map(this.transformLabValues)
  }

  async insertLabValues(labValues: Omit<LabValues, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<LabValues> {
    const id = uuidv4()
    const now = sqliteClient.getCurrentTimestamp()
    const newLabValues: LabValues = {
      ...labValues,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false
    }

    await sqliteClient.run(
      `INSERT INTO lab_values (id, patient_id, date, lab_data, created_at, updated_at, dirty, deleted) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        newLabValues.id, newLabValues.patient_id, newLabValues.date,
        JSON.stringify(newLabValues.lab_data), newLabValues.created_at,
        newLabValues.updated_at, newLabValues.dirty ? 1 : 0, newLabValues.deleted ? 1 : 0
      ]
    )

    return newLabValues
  }

  async updateLabValues(id: string, updates: Partial<LabValues>): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    const updatedData = { ...updates, updated_at: now, dirty: true }

    const fields = Object.keys(updatedData)
    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => {
      const value = updatedData[field as keyof typeof updatedData]
      if (field === 'lab_data' && typeof value === 'object') {
        return JSON.stringify(value)
      }
      if (typeof value === 'boolean') {
        return value ? 1 : 0
      }
      return value
    })

    await sqliteClient.run(
      `UPDATE lab_values SET ${setClause} WHERE id = ?`,
      [...values, id]
    )
  }

  async deleteLabValues(id: string): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    await sqliteClient.run(
      'UPDATE lab_values SET deleted = 1, dirty = 1, updated_at = ? WHERE id = ?',
      [now, id]
    )
  }

  // Transform methods to convert SQLite results to TypeScript interfaces
  private transformUnitType(row: any): UnitType {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      is_active: Boolean(row.is_active),
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  private transformPatient(row: any): Patient {
    return {
      id: row.id,
      patient_id: row.patient_id,
      name: row.name,
      gender: row.gender,
      age: row.age,
      weight: row.weight,
      admission_date: row.admission_date,
      unit_id: row.unit_id,
      main_complaint: row.main_complaint,
      medical_history: row.medical_history,
      initial_diagnosis: row.initial_diagnosis,
      is_active: Boolean(row.is_active),
      is_discharged: Boolean(row.is_discharged),
      is_deceased: Boolean(row.is_deceased),
      diseases: row.diseases ? JSON.parse(row.diseases) : [],
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  private transformVitalSigns(row: any): VitalSigns {
    return {
      id: row.id,
      patient_id: row.patient_id,
      date: row.date,
      vital_signs_data: row.vital_signs_data ? JSON.parse(row.vital_signs_data) : {},
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  private transformLabValues(row: any): LabValues {
    return {
      id: row.id,
      patient_id: row.patient_id,
      date: row.date,
      lab_data: row.lab_data ? JSON.parse(row.lab_data) : {},
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  // Medication Names
  async getMedicationNames(): Promise<MedicationName[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM medication_names WHERE deleted = 0 ORDER BY name'
    )
    return results.map(this.transformMedicationName)
  }

  async insertMedicationName(medicationName: Omit<MedicationName, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<MedicationName> {
    const id = uuidv4()
    const now = sqliteClient.getCurrentTimestamp()
    const newMedicationName: MedicationName = {
      ...medicationName,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false
    }

    await sqliteClient.run(
      `INSERT INTO medication_names (id, name, created_at, updated_at, dirty, deleted)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [newMedicationName.id, newMedicationName.name, newMedicationName.created_at,
       newMedicationName.updated_at, newMedicationName.dirty ? 1 : 0, newMedicationName.deleted ? 1 : 0]
    )

    return newMedicationName
  }

  // Medications
  async getMedications(patientId: string): Promise<Medication[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM medications WHERE patient_id = ? AND deleted = 0 ORDER BY date_prescribed DESC',
      [patientId]
    )
    return results.map(this.transformMedication)
  }

  async insertMedication(medication: Omit<Medication, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<Medication> {
    const id = uuidv4()
    const now = sqliteClient.getCurrentTimestamp()
    const newMedication: Medication = {
      ...medication,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false
    }

    await sqliteClient.run(
      `INSERT INTO medications (id, patient_id, medication_name, dosage, frequency, date_prescribed,
       is_active, created_at, updated_at, dirty, deleted)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        newMedication.id, newMedication.patient_id, newMedication.medication_name,
        newMedication.dosage || null, newMedication.frequency || null, newMedication.date_prescribed,
        newMedication.is_active ? 1 : 0, newMedication.created_at, newMedication.updated_at,
        newMedication.dirty ? 1 : 0, newMedication.deleted ? 1 : 0
      ]
    )

    return newMedication
  }

  async updateMedication(id: string, updates: Partial<Medication>): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    const updatedData = { ...updates, updated_at: now, dirty: true }

    const fields = Object.keys(updatedData)
    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => {
      const value = updatedData[field as keyof typeof updatedData]
      if (typeof value === 'boolean') {
        return value ? 1 : 0
      }
      return value
    })

    await sqliteClient.run(
      `UPDATE medications SET ${setClause} WHERE id = ?`,
      [...values, id]
    )
  }

  async deleteMedication(id: string): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    await sqliteClient.run(
      'UPDATE medications SET deleted = 1, dirty = 1, updated_at = ? WHERE id = ?',
      [now, id]
    )
  }

  // Medication Adherence
  async getMedicationAdherenceByPatient(patientId: string): Promise<MedicationAdherence[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM medication_adherence WHERE patient_id = ? AND deleted = 0 ORDER BY date DESC',
      [patientId]
    )
    return results.map(this.transformMedicationAdherence)
  }

  async upsertMedicationAdherence(record: Omit<MedicationAdherence, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<MedicationAdherence> {
    const now = sqliteClient.getCurrentTimestamp()

    // Check if record exists
    const existing = await sqliteClient.execute(
      'SELECT id FROM medication_adherence WHERE patient_id = ? AND medication_id = ? AND date = ? AND deleted = 0',
      [record.patient_id, record.medication_id, record.date]
    )

    if (existing.length > 0) {
      // Update existing record
      await sqliteClient.run(
        'UPDATE medication_adherence SET is_taking_medication = ?, updated_at = ?, dirty = 1 WHERE id = ?',
        [record.is_taking_medication ? 1 : 0, now, existing[0].id]
      )

      const updated = await sqliteClient.execute(
        'SELECT * FROM medication_adherence WHERE id = ?',
        [existing[0].id]
      )
      return this.transformMedicationAdherence(updated[0])
    } else {
      // Insert new record
      const id = uuidv4()
      const newRecord: MedicationAdherence = {
        ...record,
        id,
        created_at: now,
        updated_at: now,
        dirty: true,
        deleted: false
      }

      await sqliteClient.run(
        `INSERT INTO medication_adherence (id, patient_id, medication_id, date, is_taking_medication,
         created_at, updated_at, dirty, deleted)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          newRecord.id, newRecord.patient_id, newRecord.medication_id, newRecord.date,
          newRecord.is_taking_medication ? 1 : 0, newRecord.created_at, newRecord.updated_at,
          newRecord.dirty ? 1 : 0, newRecord.deleted ? 1 : 0
        ]
      )

      return newRecord
    }
  }

  // Doctor Notes
  async getDoctorNotes(patientId: string): Promise<DoctorNote[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM doctor_notes WHERE patient_id = ? AND deleted = 0 ORDER BY date DESC',
      [patientId]
    )
    return results.map(this.transformDoctorNote)
  }

  async insertDoctorNote(doctorNote: Omit<DoctorNote, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<DoctorNote> {
    const id = uuidv4()
    const now = sqliteClient.getCurrentTimestamp()
    const newDoctorNote: DoctorNote = {
      ...doctorNote,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false
    }

    await sqliteClient.run(
      `INSERT INTO doctor_notes (id, patient_id, date, content, doctor_name, created_at, updated_at, dirty, deleted)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        newDoctorNote.id, newDoctorNote.patient_id, newDoctorNote.date, newDoctorNote.content,
        newDoctorNote.doctor_name || null, newDoctorNote.created_at, newDoctorNote.updated_at,
        newDoctorNote.dirty ? 1 : 0, newDoctorNote.deleted ? 1 : 0
      ]
    )

    return newDoctorNote
  }

  async updateDoctorNote(id: string, updates: Partial<DoctorNote>): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    const updatedData = { ...updates, updated_at: now, dirty: true }

    const fields = Object.keys(updatedData)
    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => {
      const value = updatedData[field as keyof typeof updatedData]
      if (typeof value === 'boolean') {
        return value ? 1 : 0
      }
      return value
    })

    await sqliteClient.run(
      `UPDATE doctor_notes SET ${setClause} WHERE id = ?`,
      [...values, id]
    )
  }

  async deleteDoctorNote(id: string): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    await sqliteClient.run(
      'UPDATE doctor_notes SET deleted = 1, dirty = 1, updated_at = ? WHERE id = ?',
      [now, id]
    )
  }

  // Transform methods for new entities
  private transformMedicationName(row: any): MedicationName {
    return {
      id: row.id,
      name: row.name,
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  private transformMedication(row: any): Medication {
    return {
      id: row.id,
      patient_id: row.patient_id,
      medication_name: row.medication_name,
      dosage: row.dosage,
      frequency: row.frequency,
      date_prescribed: row.date_prescribed,
      is_active: Boolean(row.is_active),
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  private transformMedicationAdherence(row: any): MedicationAdherence {
    return {
      id: row.id,
      patient_id: row.patient_id,
      medication_id: row.medication_id,
      date: row.date,
      is_taking_medication: Boolean(row.is_taking_medication),
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  private transformDoctorNote(row: any): DoctorNote {
    return {
      id: row.id,
      patient_id: row.patient_id,
      date: row.date,
      content: row.content,
      doctor_name: row.doctor_name,
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  // Cultures
  async getCultures(patientId: string): Promise<Culture[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM cultures WHERE patient_id = ? AND deleted = 0 ORDER BY requested_date DESC',
      [patientId]
    )
    return results.map(this.transformCulture)
  }

  async insertCulture(culture: Omit<Culture, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<Culture> {
    const id = uuidv4()
    const now = sqliteClient.getCurrentTimestamp()
    const newCulture: Culture = {
      ...culture,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false
    }

    await sqliteClient.run(
      `INSERT INTO cultures (id, patient_id, requested_date, result_date, culture_type, results,
       microorganism, status, created_at, updated_at, dirty, deleted)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        newCulture.id, newCulture.patient_id, newCulture.requested_date,
        newCulture.result_date || null, newCulture.culture_type || null,
        newCulture.results || null, newCulture.microorganism || null, newCulture.status,
        newCulture.created_at, newCulture.updated_at, newCulture.dirty ? 1 : 0, newCulture.deleted ? 1 : 0
      ]
    )

    return newCulture
  }

  async updateCulture(id: string, updates: Partial<Culture>): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    const updatedData = { ...updates, updated_at: now, dirty: true }

    const fields = Object.keys(updatedData)
    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => {
      const value = updatedData[field as keyof typeof updatedData]
      if (typeof value === 'boolean') {
        return value ? 1 : 0
      }
      return value
    })

    await sqliteClient.run(
      `UPDATE cultures SET ${setClause} WHERE id = ?`,
      [...values, id]
    )
  }

  async deleteCulture(id: string): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    await sqliteClient.run(
      'UPDATE cultures SET deleted = 1, dirty = 1, updated_at = ? WHERE id = ?',
      [now, id]
    )
  }

  // Radiology
  async getRadiology(patientId: string): Promise<Radiology[]> {
    const results = await sqliteClient.execute(
      'SELECT * FROM radiology WHERE patient_id = ? AND deleted = 0 ORDER BY scan_date DESC',
      [patientId]
    )
    return results.map(this.transformRadiology)
  }

  async insertRadiology(radiology: Omit<Radiology, 'id' | 'created_at' | 'updated_at' | 'dirty' | 'deleted'>): Promise<Radiology> {
    const id = uuidv4()
    const now = sqliteClient.getCurrentTimestamp()
    const newRadiology: Radiology = {
      ...radiology,
      id,
      created_at: now,
      updated_at: now,
      dirty: true,
      deleted: false
    }

    await sqliteClient.run(
      `INSERT INTO radiology (id, patient_id, scan_type, scan_date, body_part, findings,
       status, created_at, updated_at, dirty, deleted)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        newRadiology.id, newRadiology.patient_id, newRadiology.scan_type, newRadiology.scan_date,
        newRadiology.body_part || null, newRadiology.findings || null, newRadiology.status,
        newRadiology.created_at, newRadiology.updated_at, newRadiology.dirty ? 1 : 0, newRadiology.deleted ? 1 : 0
      ]
    )

    return newRadiology
  }

  async updateRadiology(id: string, updates: Partial<Radiology>): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    const updatedData = { ...updates, updated_at: now, dirty: true }

    const fields = Object.keys(updatedData)
    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => {
      const value = updatedData[field as keyof typeof updatedData]
      if (typeof value === 'boolean') {
        return value ? 1 : 0
      }
      return value
    })

    await sqliteClient.run(
      `UPDATE radiology SET ${setClause} WHERE id = ?`,
      [...values, id]
    )
  }

  async deleteRadiology(id: string): Promise<void> {
    const now = sqliteClient.getCurrentTimestamp()
    await sqliteClient.run(
      'UPDATE radiology SET deleted = 1, dirty = 1, updated_at = ? WHERE id = ?',
      [now, id]
    )
  }

  // Transform methods for Cultures and Radiology
  private transformCulture(row: any): Culture {
    return {
      id: row.id,
      patient_id: row.patient_id,
      requested_date: row.requested_date,
      result_date: row.result_date,
      culture_type: row.culture_type,
      results: row.results,
      microorganism: row.microorganism,
      status: row.status,
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  private transformRadiology(row: any): Radiology {
    return {
      id: row.id,
      patient_id: row.patient_id,
      scan_type: row.scan_type,
      scan_date: row.scan_date,
      body_part: row.body_part,
      findings: row.findings,
      status: row.status,
      created_at: row.created_at,
      updated_at: row.updated_at,
      dirty: Boolean(row.dirty),
      deleted: Boolean(row.deleted)
    }
  }

  // Sync status methods
  async getSyncStatus(): Promise<{ pendingChanges: number; lastSync: string | null }> {
    const tables = [
      'unit_types', 'patients', 'vital_signs', 'lab_values', 'medication_names',
      'medications', 'medication_adherence', 'doctor_notes', 'cultures', 'radiology'
    ]

    let pendingChanges = 0
    let lastSync: string | null = null

    for (const table of tables) {
      // Count dirty records
      const dirtyCount = await sqliteClient.execute(
        `SELECT COUNT(*) as count FROM ${table} WHERE dirty = 1`
      )
      pendingChanges += dirtyCount[0]?.count || 0

      // Get latest timestamp
      const lastSyncResult = await sqliteClient.execute(
        `SELECT MAX(updated_at) as last_sync FROM ${table}`
      )
      const tableLastSync = lastSyncResult[0]?.last_sync
      if (tableLastSync && (!lastSync || tableLastSync > lastSync)) {
        lastSync = tableLastSync
      }
    }

    return { pendingChanges, lastSync }
  }
}

export const sqliteDataClient = new SQLiteDataClient()
