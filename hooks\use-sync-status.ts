"use client"

import { useState, useEffect, useCallback } from 'react'
import { syncEngine } from '@/lib/sync-engine'

interface SyncStatusData {
  isOnline: boolean
  isSyncing: boolean
  pendingChanges: number
  lastSync: string | null
  tableStatus: Array<{
    table: string
    pendingChanges: number
    lastSync: string | null
  }>
}

interface UseSyncStatusOptions {
  refreshInterval?: number
  autoRefresh?: boolean
}

export function useSyncStatus(options: UseSyncStatusOptions = {}) {
  const { refreshInterval = 5000, autoRefresh = true } = options
  
  const [syncStatus, setSyncStatus] = useState<SyncStatusData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadSyncStatus = useCallback(async () => {
    try {
      setError(null)
      const status = await syncEngine.getDetailedSyncStatus()
      setSyncStatus(status)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Error loading sync status:', err)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const refresh = useCallback(() => {
    setIsLoading(true)
    loadSyncStatus()
  }, [loadSyncStatus])

  const manualSync = useCallback(async () => {
    if (!syncStatus?.isOnline) {
      throw new Error('Cannot sync while offline')
    }
    
    const results = await syncEngine.manualSync()
    
    // Refresh status after sync
    await loadSyncStatus()
    
    return results
  }, [syncStatus?.isOnline, loadSyncStatus])

  useEffect(() => {
    loadSyncStatus()
    
    if (autoRefresh) {
      const interval = setInterval(loadSyncStatus, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [loadSyncStatus, autoRefresh, refreshInterval])

  return {
    syncStatus,
    isLoading,
    error,
    refresh,
    manualSync
  }
}

// Simplified hook for basic sync state
export function useSyncState(refreshInterval = 3000) {
  const [syncState, setSyncState] = useState<{
    isOnline: boolean
    isSyncing: boolean
    pendingChanges: number
  } | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadSyncState = async () => {
      try {
        const status = await syncEngine.getDetailedSyncStatus()
        setSyncState({
          isOnline: status.isOnline,
          isSyncing: status.isSyncing,
          pendingChanges: status.pendingChanges
        })
      } catch (error) {
        console.error('Error loading sync state:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadSyncState()
    const interval = setInterval(loadSyncState, refreshInterval)
    return () => clearInterval(interval)
  }, [refreshInterval])

  return { syncState, isLoading }
}
