"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { sqliteClient } from '@/lib/sqlite'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Database } from 'lucide-react'

export default function TestSQLitePage() {
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [testResults, setTestResults] = useState<string[]>([])

  useEffect(() => {
    initializeTest()
  }, [])

  const initializeTest = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      await sqliteClient.ensureInitialized()
      setIsInitialized(true)
      addTestResult('✅ SQLite initialized successfully')
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMsg)
      addTestResult(`❌ Initialization failed: ${errorMsg}`)
    } finally {
      setIsLoading(false)
    }
  }

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testBasicQuery = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      addTestResult('🧪 Testing basic SQLite query...')
      
      const results = await sqliteClient.execute('SELECT COUNT(*) as count FROM patients')
      addTestResult(`✅ Patient count: ${results[0]?.count || 0}`)
      
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMsg)
      addTestResult(`❌ Test failed: ${errorMsg}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2">
        <Database className="h-6 w-6" />
        <h1 className="text-2xl font-bold">SQLite Test</h1>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Database Status</CardTitle>
          <CardDescription>Current state of SQLite database</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>SQLite Initialized:</span>
            <Badge variant={isInitialized ? "default" : "secondary"}>
              {isInitialized ? "Yes" : "No"}
            </Badge>
          </div>
          
          <Button 
            onClick={testBasicQuery} 
            disabled={isLoading || !isInitialized}
            variant="outline"
          >
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
            Test Basic Query
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
          <CardDescription>Real-time test output and logs</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-4 rounded-lg max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-muted-foreground">No test results yet.</p>
            ) : (
              <div className="space-y-1 font-mono text-sm">
                {testResults.map((result, index) => (
                  <div key={index}>{result}</div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
